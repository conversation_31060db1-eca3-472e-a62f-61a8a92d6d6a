server.port=7001
project.name=uni-smpp-accept-service
spring.application.name=uni-smpp-accept-service

spring.profiles.active=@spring.profiles.active@

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=10
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.pool-prepared-statement-per-connection-size=20
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500

dubbo.scan.basePackages=com.uni.smpp.accept.service.service.dubbo
dubbo.application.name=${project.name}
logging.level.org.apache.dubbo=DEBUG

mybatis-plus.mapper-locations=classpath*:mybatis/*.xml

spring.unitouch.logaspect.enabled=true

# RocketMq
#spring.unitouch.mq.enabled=true
#spring.unitouch.mq.groupId=GID_UNI_SMPP_ACCEPT
#spring.unitouch.mq.consumeThreadCount=300
rocketmq.smpp.accept.ack.topic=TOPIC_SMPP_ACCEPT_ACK


# 缓存开启
spring.unitouch.cache.enabled=true


#定时任务开启
#spring.unitouch.consistent-hash.enabled=true

