package com.uni.touch.smpp.accept.service.listener;

import com.alibaba.fastjson2.TypeReference;
import com.cloudhopper.smpp.SmppServerSession;
import com.cloudhopper.smpp.pdu.DeliverSm;
import com.uni.touch.boot.mq.AbstractMqListener;
import com.uni.touch.channel.api.dto.SmsSendRecordAckDTO;
import com.uni.touch.smpp.accept.service.manager.SmppSessionManager;
import com.uni.touch.smpp.accept.service.processor.SmppDeliveryProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

/**
 * 短信发送的回执消息处理
 * 基于ch-smpp框架标准，通过appId查找SMPP会话并推送回执
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Service
public class SmsSendAckMqListener extends AbstractMqListener<SmsSendRecordAckDTO> {

    @Autowired
    private SmppSessionManager sessionManager;

    @Autowired
    private SmppDeliveryProcessor deliveryProcessor;

    @Value("${rocketmq.smpp.accept.ack.topic}")
    private String TOPIC;

    @Override
    protected ConsumeResult handleMessage(SmsSendRecordAckDTO ackDTO, MessageView messageView) {
        try {
            log.info("收到短信回执MQ消息 - messageId: {}, appId: {}, result: {}, topic: {}",
                    ackDTO.getMessageId(), ackDTO.getAppId(), ackDTO.getResult(), messageView.getTopic());

            // 1. 参数校验
            if (ackDTO.getAppId() == null) {
                log.warn("回执消息缺少appId - messageId: {}", ackDTO.getMessageId());
                return ConsumeResult.SUCCESS; // 跳过无效消息
            }

            // 2. 根据appId查找活跃的SMPP会话
            SmppServerSession session = sessionManager.findActiveSessionByAppId(ackDTO.getAppId());
            if (session == null) {
                log.warn("未找到活跃的SMPP会话 - appId: {}, messageId: {}",
                        ackDTO.getAppId(), ackDTO.getMessageId());
                return ConsumeResult.SUCCESS; // 跳过无会话的消息
            }

            // 3. 推送回执到SMPP会话
            pushDeliveryReceiptToSession(session, ackDTO);

            return ConsumeResult.SUCCESS;

        } catch (Exception e) {
            log.error("处理短信回执MQ消息失败 - messageId: {}, appId: {}, error: {}",
                    ackDTO.getMessageId(), ackDTO.getAppId(), e.getMessage(), e);
            return ConsumeResult.FAILURE;
        }
    }

    /**
     * 推送回执到单个SMPP会话
     */
    private void pushDeliveryReceiptToSession(SmppServerSession session, SmsSendRecordAckDTO ackDTO) {
        try {
            String systemId = session.getConfiguration().getSystemId();

            // 1. 流控检查
            if (!deliveryProcessor.checkRateLimit(systemId)) {
                log.info("回执推送速度超限 - systemId: {}, messageId: {}",
                        systemId, ackDTO.getMessageId());
                return;
            }

            // 2. 构造DeliverSm回执
            DeliverSm deliverSm = deliveryProcessor.buildDeliveryReceipt(ackDTO, systemId);
            if (deliverSm == null) {
                log.error("构造回执失败 - systemId: {}, messageId: {}",
                        systemId, ackDTO.getMessageId());
                return;
            }

            // 3. 异步推送回执
            session.sendRequestPdu(deliverSm, 30000, false);

            log.info("SMPP回执推送成功 - sessionId: {}, messageId: {}, appId: {}, result: {}",
                    session.getConfiguration().getName(), ackDTO.getMessageId(),
                    ackDTO.getAppId(), ackDTO.getResult());

        } catch (Exception e) {
            log.error("SMPP回执推送异常 - sessionId: {}, messageId: {}, appId: {}",
                    session.getConfiguration().getName(), ackDTO.getMessageId(),
                    ackDTO.getAppId(), e);
        }
    }

    @Override
    protected Type getReflectType() {
        return new TypeReference<SmsSendRecordAckDTO>() {
        }.getType();
    }

    @Override
    public String getTopic() {
        return TOPIC;
    }

    @Override
    public String getTag() {
        return null;
    }
}
