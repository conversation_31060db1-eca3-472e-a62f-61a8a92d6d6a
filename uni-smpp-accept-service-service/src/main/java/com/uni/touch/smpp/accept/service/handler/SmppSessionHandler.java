package com.uni.touch.smpp.accept.service.handler;

import com.cloudhopper.smpp.PduAsyncResponse;
import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.impl.DefaultSmppSessionHandler;
import com.cloudhopper.smpp.pdu.*;
import com.cloudhopper.smpp.tlv.Tlv;
import com.cloudhopper.smpp.util.SmppUtil;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import com.uni.touch.smpp.accept.service.entity.*;
import com.uni.touch.smpp.accept.service.limiter.SmppRateLimiter;
import com.uni.touch.smpp.accept.service.manager.SmppSegmentManager;
import com.uni.touch.smpp.accept.service.processor.SmppSubmitProcessor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * SMPP会话处理器
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
public class SmppSessionHandler extends DefaultSmppSessionHandler {

    private final Long sessionId;
    private final SmppAccountInfo account;
    private final SmppSegmentManager segmentManager;
    private final SmppRateLimiter rateLimiter;
    private final SmppSubmitProcessor submitProcessor;
    private final SmppEncodingConverter encodingConverter;

    public SmppSessionHandler(Long sessionId, SmppAccountInfo account,
                            SmppSegmentManager segmentManager,
                            SmppRateLimiter rateLimiter,
                            SmppSubmitProcessor submitProcessor,
                            SmppEncodingConverter encodingConverter) {
        this.sessionId = sessionId;
        this.account = account;
        this.segmentManager = segmentManager;
        this.rateLimiter = rateLimiter;
        this.submitProcessor = submitProcessor;
        this.encodingConverter = encodingConverter;
    }

    @Override
    public PduResponse firePduRequestReceived(PduRequest pduRequest) {
        log.debug("收到PDU请求 - sessionId: {}, systemId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, account.getSystemId(), Integer.toHexString(pduRequest.getCommandId()),
                pduRequest.getSequenceNumber());

        try {
            switch (pduRequest.getCommandId()) {
                case SmppConstants.CMD_ID_SUBMIT_SM:
                    return handleSubmitSm((SubmitSm) pduRequest);

                case SmppConstants.CMD_ID_ENQUIRE_LINK:
                    return handleEnquireLink((EnquireLink) pduRequest);

                case SmppConstants.CMD_ID_UNBIND:
                    return handleUnbind((Unbind) pduRequest);

                default:
                    log.warn("不支持的PDU类型 - sessionId: {}, commandId: 0x{}",
                        sessionId, Integer.toHexString(pduRequest.getCommandId()));
                    PduResponse response = pduRequest.createResponse();
                    response.setCommandStatus(SmppConstants.STATUS_INVCMDID);
                    return response;
            }

        } catch (Exception e) {
            log.error("处理PDU请求异常 - sessionId: {}, commandId: 0x{}",
                    sessionId, Integer.toHexString(pduRequest.getCommandId()), e);

            PduResponse response = pduRequest.createResponse();
            response.setCommandStatus(SmppConstants.STATUS_SYSERR);
            return response;
        }
    }

    /**
     * 处理短信提交请求
     */
    private SubmitSmResp handleSubmitSm(SubmitSm submitSm) {
        try {
            String systemId = account.getSystemId();

            // 打印PDU核心参数
            logPduRequestDetails(submitSm, systemId);

            // 1. 流控检查
            if (!checkRateLimit(systemId)) {
                log.warn("提交速度超限 - systemId: {}", systemId);
                return createErrorResponse(submitSm, SmppConstants.STATUS_THROTTLED);
            }

            // 2. 编码校验
            // if (!validateClientDataCoding(submitSm)) {
            //     log.warn("数据编码校验失败 - systemId: {}, data_coding: 0x{}",
            //             systemId, Integer.toHexString(submitSm.getDataCoding()));
            //     return createErrorResponse(submitSm, SmppConstants.STATUS_INVDCS);
            // }

            // 3. 解析消息信息
            SmppMessageInfo messageInfo = parseMessageInfo(submitSm);
            if (messageInfo == null) {
                return createErrorResponse(submitSm, SmppConstants.STATUS_INVMSGLEN);
            }

            // 4. 根据消息类型处理
            return processMessage(submitSm, messageInfo);

        } catch (Exception e) {
            log.error("处理SubmitSm异常 - sessionId: {}", sessionId, e);
            return createErrorResponse(submitSm, SmppConstants.STATUS_SYSERR);
        }
    }

    /**
     * 打印PDU请求的核心参数
     */
    private void logPduRequestDetails(SubmitSm submitSm, String systemId) {
        log.info("=== PDU请求详情 ===");
        log.info("会话信息 - sessionId: {}, systemId: {}, 线程: {}",
                sessionId, systemId, Thread.currentThread().getName());
        log.info("地址信息 - 源地址: {}, 目标地址: {}",
                submitSm.getSourceAddress().getAddress(),
                submitSm.getDestAddress().getAddress());
        log.info("编码信息 - data_coding: 0x{}, 服务端encoding: {}",
                Integer.toHexString(submitSm.getDataCoding()),
                account.getEncoding());
        log.info("消息信息 - esm_class: 0x{}, protocol_id: {}",
                Integer.toHexString(submitSm.getEsmClass()),
                submitSm.getProtocolId());

        // 打印短消息内容
        byte[] shortMessage = submitSm.getShortMessage();
        if (shortMessage != null && shortMessage.length > 0) {
            log.info("短消息 - 长度: {}, 原始字节: {}",
                    shortMessage.length, bytesToHexString(shortMessage));
        }

        // 打印可选参数
        if (submitSm.hasOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD)) {
            byte[] payload = submitSm.getOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD).getValue();
            log.info("message_payload - 长度: {}, 原始字节: {}",
                    payload.length, bytesToHexString(payload));
        }

        // 打印SAR相关TLV
        if (hasSarTlvTags(submitSm)) {
            log.info("SAR分段 - ref_num: {}, total: {}, seq: {}",
                    getIntFromTlv(submitSm, SmppConstants.TAG_SAR_MSG_REF_NUM),
                    getIntFromTlv(submitSm, SmppConstants.TAG_SAR_TOTAL_SEGMENTS),
                    getIntFromTlv(submitSm, SmppConstants.TAG_SAR_SEGMENT_SEQNUM));
        }

        log.info("=== PDU请求详情结束 ===");
    }

    /**
     * 校验客户端data_coding是否符合服务端配置
     */
    private boolean validateClientDataCoding(SubmitSm submitSm) {
        byte clientDataCoding = submitSm.getDataCoding();
        Integer serverEncoding = account.getEncoding();

        return encodingConverter.validateDataCoding(clientDataCoding, serverEncoding);
    }

    /**
     * 处理心跳请求
     */
    private EnquireLinkResp handleEnquireLink(EnquireLink enquireLink) {
        log.debug("收到心跳请求 - sessionId: {}, systemId: {}", sessionId, account.getSystemId());
        return enquireLink.createResponse();
    }

    /**
     * 处理解绑请求
     */
    private UnbindResp handleUnbind(Unbind unbind) {
        log.info("收到解绑请求 - sessionId: {}, systemId: {}", sessionId, account.getSystemId());
        return unbind.createResponse();
    }

    @Override
    public void fireExpectedPduResponseReceived(PduAsyncResponse pduAsyncResponse) {
        log.debug("收到预期PDU响应 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, 
                Integer.toHexString(pduAsyncResponse.getResponse().getCommandId()),
                pduAsyncResponse.getResponse().getSequenceNumber());
        
        super.fireExpectedPduResponseReceived(pduAsyncResponse);
    }

    @Override
    public void fireUnexpectedPduResponseReceived(PduResponse pduResponse) {
        log.warn("收到意外PDU响应 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, 
                Integer.toHexString(pduResponse.getCommandId()),
                pduResponse.getSequenceNumber());
        
        super.fireUnexpectedPduResponseReceived(pduResponse);
    }

    @Override
    public void fireChannelUnexpectedlyClosed() {
        log.warn("会话通道意外关闭 - sessionId: {}", sessionId);
        super.fireChannelUnexpectedlyClosed();
    }

    @Override
    public void firePduRequestExpired(PduRequest pduRequest) {
        log.warn("PDU请求超时 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId,
                Integer.toHexString(pduRequest.getCommandId()),
                pduRequest.getSequenceNumber());

        super.firePduRequestExpired(pduRequest);
    }

    // ==================== 私有方法 ====================

    /**
     * 检查流控限制
     */
    private boolean checkRateLimit(String systemId) {
        return rateLimiter.getSubmitRateLimiter(systemId, account.getMaxSubmitSpeed())
                .tryAcquire(1, 100, TimeUnit.MILLISECONDS);
    }

    /**
     * 解析消息信息（统一使用服务端编码配置）
     */
    private SmppMessageInfo parseMessageInfo(SubmitSm submitSm) {
        try {
            Integer serverEncoding = account.getEncoding();
            byte serverDataCoding = encodingConverter.toDataCoding(serverEncoding);

            log.info("开始解析消息 - 服务端编码: {}, 对应data_coding: 0x{}",
                    serverEncoding, Integer.toHexString(serverDataCoding));

            // 1. 检查SAR TLV标签（优先级最高）
            if (hasSarTlvTags(submitSm)) {
                log.info("检测到SAR分段消息");
                SmppSarInfo sarInfo = parseSarInfo(submitSm);
                if (sarInfo != null && sarInfo.isValid()) {
                    log.info("SAR分段信息解析成功 - ref: {}, total: {}, seq: {}",
                            sarInfo.getRefNum(), sarInfo.getTotalSegments(), sarInfo.getSegmentSeq());
                    return SmppMessageInfo.createSarSegmented(sarInfo, serverDataCoding);
                }
            }

            // 2. 检查message_payload
            if (submitSm.hasOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD)) {
                log.info("检测到message_payload消息");
                Tlv payloadTlv = submitSm.getOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD);
                if (payloadTlv != null && payloadTlv.getValue() != null) {
                    byte[] payloadBytes = payloadTlv.getValue();
                    log.info("message_payload原始数据 - 长度: {}, 字节: {}",
                            payloadBytes.length, bytesToHexString(payloadBytes));

                    String content = encodingConverter.decode(payloadBytes, serverEncoding);
                    log.info("message_payload解码完成 - 内容: '{}'", content);
                    return SmppMessageInfo.createMessagePayload(content, serverDataCoding);
                }
            }

            // 3. 检查UDH
            if (SmppUtil.isUserDataHeaderIndicatorEnabled(submitSm.getEsmClass())) {
                log.info("检测到UDH分段消息");
                SmppUdhInfo udhInfo = parseUdhInfo(submitSm);
                if (udhInfo != null && udhInfo.isValid()) {
                    log.info("UDH分段信息解析成功 - ref: {}, total: {}, part: {}",
                            udhInfo.getRefNum(), udhInfo.getTotalParts(), udhInfo.getPartNum());
                    return SmppMessageInfo.createUdhSegmented(udhInfo, serverDataCoding);
                }
            }

            // 4. 普通消息
            byte[] messageBytes = submitSm.getShortMessage();
            if (messageBytes != null) {
                log.info("检测到普通消息");
                log.info("普通消息原始数据 - 长度: {}, 字节: {}",
                        messageBytes.length, bytesToHexString(messageBytes));

                String content = encodingConverter.decode(messageBytes, serverEncoding);
                log.info("普通消息解码完成 - 内容: '{}'", content);
                return SmppMessageInfo.createRegular(content, serverDataCoding);
            }

            log.warn("未找到有效的消息内容");
            return null;

        } catch (Exception e) {
            log.error("解析消息信息异常", e);
            return null;
        }
    }

    /**
     * 处理消息
     */
    private SubmitSmResp processMessage(SubmitSm submitSm, SmppMessageInfo messageInfo) {
        switch (messageInfo.getMessageType()) {
            case SAR_SEGMENTED:
                return handleSarSegmentedMessage(submitSm, messageInfo.getSarInfo());

            case UDH_SEGMENTED:
                return handleUdhSegmentedMessage(submitSm, messageInfo.getUdhInfo());

            case MESSAGE_PAYLOAD:
            case REGULAR:
                return handleCompleteMessage(submitSm, messageInfo.getContent());

            default:
                log.warn("未知消息类型 - systemId: {}", account.getSystemId());
                return createErrorResponse(submitSm, SmppConstants.STATUS_INVMSGLEN);
        }
    }

    /**
     * 处理SAR分段消息
     */
    private SubmitSmResp handleSarSegmentedMessage(SubmitSm submitSm, SmppSarInfo sarInfo) {
        log.info("处理SAR分段消息 - systemId: {}, 参考号: {}, 分段: {}/{}",
                account.getSystemId(), sarInfo.getRefNum(),
                sarInfo.getSegmentSeq(), sarInfo.getTotalSegments());

        // 处理分段重组（传递服务端编码）
        SegmentReassembleInfo reassembled =
            segmentManager.processSarSegment(submitSm, account.getSystemId(), sarInfo, account.getEncoding());

        if (reassembled != null) {
            // 重组完成，提交完整消息
            log.info("SAR分段重组完成，提交完整消息 - 内容长度: {}", reassembled.getCompleteMessage().length());
            return handleCompleteMessage(reassembled.getOriginalSubmitSm(), reassembled.getCompleteMessage());
        } else {
            // 分段缓存中，返回成功响应
            String messageId = generateMessageId();
            log.info("SAR分段缓存成功 - messageId: {}", messageId);
            return createSuccessResponse(submitSm, messageId);
        }
    }

    /**
     * 处理UDH分段消息
     */
    private SubmitSmResp handleUdhSegmentedMessage(SubmitSm submitSm, SmppUdhInfo udhInfo) {
        log.info("处理UDH分段消息 - systemId: {}, 参考号: {}, 分段: {}/{}",
                account.getSystemId(), udhInfo.getRefNum(),
                udhInfo.getPartNum(), udhInfo.getTotalParts());

        // 处理分段重组（传递服务端编码）
        SegmentReassembleInfo reassembled =
            segmentManager.processUdhSegment(submitSm, account.getSystemId(), udhInfo, account.getEncoding());

        if (reassembled != null) {
            // 重组完成，提交完整消息
            log.info("UDH分段重组完成，提交完整消息 - 内容长度: {}", reassembled.getCompleteMessage().length());
            return handleCompleteMessage(reassembled.getOriginalSubmitSm(), reassembled.getCompleteMessage());
        } else {
            // 分段缓存中，返回成功响应
            String messageId = generateMessageId();
            log.info("UDH分段缓存成功 - messageId: {}", messageId);
            return createSuccessResponse(submitSm, messageId);
        }
    }

    /**
     * 处理完整消息
     */
    private SubmitSmResp handleCompleteMessage(SubmitSm submitSm, String message) {
        try {
            // 调用业务服务提交消息
            String messageId = submitProcessor.submitMessage(submitSm, message, account);

            if (messageId != null) {
                log.info("消息提交成功 - systemId: {}, messageId: {}, 长度: {}",
                        account.getSystemId(), messageId, message.length());
                return createSuccessResponse(submitSm, messageId);
            } else {
                log.warn("消息提交失败 - systemId: {}", account.getSystemId());
                return createErrorResponse(submitSm, SmppConstants.STATUS_SUBMITFAIL);
            }

        } catch (Exception e) {
            log.error("提交消息异常 - systemId: {}", account.getSystemId(), e);
            return createErrorResponse(submitSm, SmppConstants.STATUS_SYSERR);
        }
    }

    // ==================== 解析方法 ====================

    /**
     * 检查是否有SAR TLV标签
     */
    private boolean hasSarTlvTags(SubmitSm submitSm) {
        return submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_MSG_REF_NUM)
            && submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_TOTAL_SEGMENTS)
            && submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_SEGMENT_SEQNUM);
    }

    /**
     * 解析SAR信息
     */
    private SmppSarInfo parseSarInfo(SubmitSm submitSm) {
        try {
            Tlv refNumTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_MSG_REF_NUM);
            Tlv totalSegTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_TOTAL_SEGMENTS);
            Tlv segSeqTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_SEGMENT_SEQNUM);

            if (refNumTlv == null || totalSegTlv == null || segSeqTlv == null) {
                return null;
            }

            SmppSarInfo sarInfo = new SmppSarInfo();
            sarInfo.setRefNum(getSarTlvValue(refNumTlv));
            sarInfo.setTotalSegments(getSarTlvValue(totalSegTlv));
            sarInfo.setSegmentSeq(getSarTlvValue(segSeqTlv));

            return sarInfo;

        } catch (Exception e) {
            log.error("解析SAR信息异常", e);
            return null;
        }
    }

    /**
     * 解析UDH信息
     */
    private SmppUdhInfo parseUdhInfo(SubmitSm submitSm) {
        byte[] shortMessage = submitSm.getShortMessage();
        if (shortMessage == null || shortMessage.length < 6) {
            return null;
        }

        try {
            int offset = 0;
            int udhl = shortMessage[offset++] & 0xFF;

            if (shortMessage.length < udhl + 1) {
                return null;
            }

            SmppUdhInfo udhInfo = new SmppUdhInfo();
            int udhEnd = offset + udhl;

            while (offset < udhEnd) {
                int iei = shortMessage[offset++] & 0xFF;
                if (offset >= udhEnd) break;

                int iedl = shortMessage[offset++] & 0xFF;
                if (offset + iedl > udhEnd) break;

                if (iei == 0x00 && iedl == 3) {
                    udhInfo.setRefNum(shortMessage[offset] & 0xFF);
                    udhInfo.setTotalParts(shortMessage[offset + 1] & 0xFF);
                    udhInfo.setPartNum(shortMessage[offset + 2] & 0xFF);
                } else if (iei == 0x08 && iedl == 4) {
                    udhInfo.setRefNum(((shortMessage[offset] & 0xFF) << 8) | (shortMessage[offset + 1] & 0xFF));
                    udhInfo.setTotalParts(shortMessage[offset + 2] & 0xFF);
                    udhInfo.setPartNum(shortMessage[offset + 3] & 0xFF);
                }

                offset += iedl;
            }

            if (udhInfo.getRefNum() > 0 && udhInfo.getTotalParts() > 0 && udhInfo.getPartNum() > 0) {
                int userDataLength = shortMessage.length - udhl - 1;
                udhInfo.setUserData(new byte[userDataLength]);
                System.arraycopy(shortMessage, udhl + 1, udhInfo.getUserData(), 0, userDataLength);
                return udhInfo;
            }

        } catch (Exception e) {
            log.error("解析UDH信息异常", e);
        }

        return null;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取数据编码（使用新的方法名）
     */
    private byte getDataCoding() {
        return encodingConverter.toDataCoding(account.getEncoding());
    }

    /**
     * 字节数组转十六进制字符串（用于日志）
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, 50); i++) { // 最多显示50个字节
            sb.append(String.format("%02X ", bytes[i]));
        }
        if (bytes.length > 50) {
            sb.append("...(").append(bytes.length).append(" bytes total)");
        }
        return sb.toString().trim();
    }

    /**
     * 从TLV中获取整数值
     */
    private int getIntFromTlv(SubmitSm submitSm, short tag) {
        if (submitSm.hasOptionalParameter(tag)) {
            Tlv tlv = submitSm.getOptionalParameter(tag);
            return getSarTlvValue(tlv);
        }
        return 0;
    }

    /**
     * 获取SAR TLV值
     */
    private int getSarTlvValue(Tlv tlv) {
        byte[] value = tlv.getValue();
        if (value.length == 1) {
            return value[0] & 0xFF;
        } else if (value.length == 2) {
            return ((value[0] & 0xFF) << 8) | (value[1] & 0xFF);
        }
        return 0;
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 创建成功响应
     */
    private SubmitSmResp createSuccessResponse(SubmitSm submitSm, String messageId) {
        SubmitSmResp response = submitSm.createResponse();
        response.setMessageId(messageId);
        response.setCommandStatus(SmppConstants.STATUS_OK);
        return response;
    }

    /**
     * 创建错误响应
     */
    private SubmitSmResp createErrorResponse(SubmitSm submitSm, int status) {
        SubmitSmResp response = submitSm.createResponse();
        response.setCommandStatus(status);
        return response;
    }
}
