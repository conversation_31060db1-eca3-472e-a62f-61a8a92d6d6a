package com.uni.touch.smpp.accept.service.processor;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.pdu.DeliverSm;
import com.cloudhopper.smpp.tlv.Tlv;
import com.cloudhopper.smpp.type.Address;
import com.cloudhopper.smpp.util.DeliveryReceipt;
import com.uni.touch.smpp.accept.service.entity.SmppAccountInfo;
import com.uni.touch.smpp.accept.service.manager.SmppSessionManager;
import com.uni.touch.smpp.accept.service.limiter.SmppRateLimiter;
import com.uni.touch.channel.api.dto.SmsSendRecordAckDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * SMPP回执处理器
 * 基于ch-smpp框架标准
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Component
public class SmppDeliveryProcessor {

    @Autowired
    private SmppSessionManager sessionManager;

    @Autowired
    private SmppRateLimiter rateLimiter;

    /**
     * 构造标准的DeliverSm回执
     */
    public DeliverSm buildDeliveryReceipt(SmsSendRecordAckDTO ackDTO, String systemId) {
        try {
            log.info("构造SMPP回执 - messageId: {}, appId: {}, result: {}, systemId: {}", 
                    ackDTO.getMessageId(), ackDTO.getAppId(), ackDTO.getResult(), systemId);

            DeliverSm deliverSm = new DeliverSm();
            
            // 1. 设置地址信息
            deliverSm.setSourceAddress(new Address((byte)1, (byte)1, ackDTO.getSenderId()));
            deliverSm.setDestAddress(new Address((byte)0, (byte)0, systemId));
            
            // 2. 设置ESM Class为状态报告
            deliverSm.setEsmClass(SmppConstants.ESM_CLASS_MT_SMSC_DELIVERY_RECEIPT);
            
            // 3. 设置数据编码
            deliverSm.setDataCoding(SmppConstants.DATA_CODING_DEFAULT);
            
            // 4. 构造DeliveryReceipt标准格式内容
            DeliveryReceipt receipt = createDeliveryReceipt(ackDTO);
            
            // 5. 生成标准格式的状态报告文本
            String receiptText = receipt.toShortMessage();
            deliverSm.setShortMessage(receiptText.getBytes(StandardCharsets.ISO_8859_1));
            
            // 6. 设置可选参数
            deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_RECEIPTED_MSG_ID, 
                    ackDTO.getMessageId().getBytes()));
            deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_MSG_STATE, 
                    new byte[]{mapResultToSmppState(ackDTO.getResult())}));
            
            log.info("SMPP回执构造完成 - messageId: {}, receiptText: {}", 
                    ackDTO.getMessageId(), receiptText);
            
            return deliverSm;
            
        } catch (Exception e) {
            log.error("构造SMPP回执异常 - messageId: {}, appId: {}", 
                    ackDTO.getMessageId(), ackDTO.getAppId(), e);
            return null;
        }
    }

    /**
     * 检查流控限制
     */
    public boolean checkRateLimit(String systemId) {
        SmppAccountInfo account = sessionManager.getAccountBySystemId(systemId);
        if (account != null && account.getMaxDlrSpeed() != null) {
            try {
                return rateLimiter.getDlrRateLimiter(systemId, account.getMaxDlrSpeed())
                        .tryAcquire(1, 100, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.warn("流控检查异常 - systemId: {}", systemId, e);
                return true; // 异常时允许通过
            }
        }
        return true; // 无限制时允许通过
    }

    /**
     * 构造DeliveryReceipt对象
     */
    private DeliveryReceipt createDeliveryReceipt(SmsSendRecordAckDTO ackDTO) {
        DeliveryReceipt receipt = new DeliveryReceipt();
        
        // 设置消息ID
        receipt.setMessageId(ackDTO.getMessageId());
        
        // 设置投递状态
        byte state = mapResultToSmppState(ackDTO.getResult());
        receipt.setState(state);
        
        // 设置提交和投递计数
        receipt.setSubmitCount(1);
        receipt.setDeliveredCount(ackDTO.getResult() == 1 ? 1 : 0);
        
        // 设置时间信息
        DateTime submitTime = convertToDateTime(ackDTO.getTime());
        DateTime doneTime = new DateTime(); // 当前时间作为完成时间
        receipt.setSubmitDate(submitTime);
        receipt.setDoneDate(doneTime);
        
        // 设置错误码
        receipt.setErrorCode(parseErrorCode(ackDTO.getErrCode()));
        
        // 设置原始短信内容（前20个字符）
        String text = StringUtils.isNotBlank(ackDTO.getErrMsg()) ? ackDTO.getErrMsg() : "";
        receipt.setText(truncateText(text, 20));
        
        return receipt;
    }

    /**
     * 映射result字段到SMPP状态
     */
    private byte mapResultToSmppState(Integer result) {
        if (result == null) {
            return SmppConstants.STATE_UNKNOWN;
        }
        
        switch (result) {
            case 1:
                return SmppConstants.STATE_DELIVERED;      // 送达成功
            case 2:
                return SmppConstants.STATE_UNDELIVERABLE;  // 送达失败
            default:
                return SmppConstants.STATE_UNKNOWN;        // 未知状态
        }
    }

    /**
     * 解析错误码
     */
    private int parseErrorCode(String errCode) {
        if (StringUtils.isBlank(errCode)) {
            return 0;
        }
        
        try {
            return Integer.parseInt(errCode);
        } catch (NumberFormatException e) {
            log.warn("无效的错误码格式: {}", errCode);
            return 0;
        }
    }

    /**
     * 转换Date到DateTime
     */
    private DateTime convertToDateTime(Date date) {
        if (date == null) {
            return new DateTime().minusMinutes(1); // 默认1分钟前
        }
        return new DateTime(date);
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (StringUtils.isBlank(text)) {
            return "";
        }
        
        if (text.length() <= maxLength) {
            return text;
        }
        
        return text.substring(0, maxLength);
    }
}
