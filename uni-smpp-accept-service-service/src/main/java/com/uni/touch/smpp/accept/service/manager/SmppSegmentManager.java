package com.uni.touch.smpp.accept.service.manager;

import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.smpp.accept.service.entity.*;
import com.uni.touch.smpp.accept.service.processor.SmppSegmentProcessor;
import com.uni.touch.smpp.accept.service.utils.SegmentValidateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SMPP分段消息管理器
 * 支持SAR TLV和UDH两种方式
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppSegmentManager {

    @Autowired
    private SmppSegmentProcessor smppSegmentProcessor;

    @Autowired
    private SegmentValidateUtils segmentValidateUtils;

    /**
     * 分段消息组缓存
     */
    private final Map<String, SegmentMessageGroup> segmentCache = new ConcurrentHashMap<>();

    /**
     * 分段消息超时时间（分钟）
     */
    private static final int SEGMENT_TIMEOUT_MINUTES = 5;

    /**
     * 处理SAR分段消息
     */
    public SegmentReassembleInfo processSarSegment(SubmitSm submitSm, String systemId, SmppSarInfo sarInfo, Integer serverEncoding) {
        // 基本验证
        if (!segmentValidateUtils.validateSegmentMessage(submitSm, systemId)) {
            return null;
        }

        String cacheKey = sarInfo.generateCacheKey(systemId);

        log.info("处理SAR分段 - systemId: {}, ref: {}, seq: {}/{}, 服务端编码: {}",
                systemId, sarInfo.getRefNum(), sarInfo.getSegmentSeq(), sarInfo.getTotalSegments(), serverEncoding);

        // 获取或创建分段组
        SegmentMessageGroup group = segmentCache.computeIfAbsent(cacheKey, k ->
            new SegmentMessageGroup(cacheKey, systemId, sarInfo.getRefNum(),
                           sarInfo.getTotalSegments(), SegmentType.SAR));

        // 验证SAR分段一致性
        if (!segmentValidateUtils.validateSarConsistency(group, sarInfo)) {
            return null;
        }

        // 添加分段
        if (!group.addSegment(sarInfo.getSegmentSeq(), submitSm)) {
            log.error("添加SAR分段失败 - key: {}, 分段号: {}", cacheKey, sarInfo.getSegmentSeq());
            return null;
        }

        log.info("SAR分段添加成功 - key: {}, 已收集: {}/{}",
                cacheKey, group.getCollectedSegmentCount(), group.getTotalSegments());

        // 检查是否完整并重组
        if (group.isComplete()) {
            log.info("SAR分段收集完整，开始重组 - key: {}", cacheKey);
            SegmentReassembleInfo reassembled = smppSegmentProcessor.reassemble(group, serverEncoding);
            if (reassembled != null) {
                segmentCache.remove(cacheKey);
                log.info("SAR分段消息重组完成 - systemId: {}, 参考号: {}, 总分段: {}",
                        systemId, sarInfo.getRefNum(), sarInfo.getTotalSegments());
            }
            return reassembled;
        }

        return null;
    }

    /**
     * 处理UDH分段消息
     */
    public SegmentReassembleInfo processUdhSegment(SubmitSm submitSm, String systemId, SmppUdhInfo udhInfo, Integer serverEncoding) {
        // 基本验证
        if (!segmentValidateUtils.validateSegmentMessage(submitSm, systemId)) {
            return null;
        }

        String cacheKey = udhInfo.generateCacheKey(systemId);

        log.info("处理UDH分段 - systemId: {}, ref: {}, part: {}/{}, 服务端编码: {}",
                systemId, udhInfo.getRefNum(), udhInfo.getPartNum(), udhInfo.getTotalParts(), serverEncoding);

        // 获取或创建分段组
        SegmentMessageGroup group = segmentCache.computeIfAbsent(cacheKey, k ->
            new SegmentMessageGroup(cacheKey, systemId, udhInfo.getRefNum(),
                           udhInfo.getTotalParts(), SegmentType.UDH));

        // 验证UDH分段一致性
        if (!segmentValidateUtils.validateUdhConsistency(group, udhInfo)) {
            return null;
        }

        // 创建包含UDH用户数据的SubmitSm
        SubmitSm udhSubmitSm = createUdhSubmitSm(submitSm, udhInfo.getUserData());

        // 添加分段
        if (!group.addSegment(udhInfo.getPartNum(), udhSubmitSm)) {
            log.error("添加UDH分段失败 - key: {}, 分段号: {}", cacheKey, udhInfo.getPartNum());
            return null;
        }

        log.info("UDH分段添加成功 - key: {}, 已收集: {}/{}",
                cacheKey, group.getCollectedSegmentCount(), group.getTotalSegments());

        // 检查是否完整并重组
        if (group.isComplete()) {
            log.info("UDH分段收集完整，开始重组 - key: {}", cacheKey);
            SegmentReassembleInfo reassembled = smppSegmentProcessor.reassemble(group, serverEncoding);
            if (reassembled != null) {
                segmentCache.remove(cacheKey);
                log.info("UDH分段消息重组完成 - systemId: {}, 参考号: {}, 总分段: {}",
                        systemId, udhInfo.getRefNum(), udhInfo.getTotalParts());
            }
            return reassembled;
        }

        return null;
    }





    /**
     * 定期清理过期分段
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanupExpiredSegments() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(SEGMENT_TIMEOUT_MINUTES);
        
        segmentCache.entrySet().removeIf(entry -> {
            SegmentMessageGroup group = entry.getValue();
            if (group.getLastUpdateTime().isBefore(expireTime)) {
                log.warn("清理过期分段消息 - key: {}, 已收集: {}/{}, 类型: {}", 
                        group.getCacheKey(), group.getSegments().size(), 
                        group.getTotalSegments(), group.getType());
                return true;
            }
            return false;
        });
    }



    private SubmitSm createUdhSubmitSm(SubmitSm original, byte[] userData) {
        try {
            // 创建新的SubmitSm，包含UDH解析后的用户数据
            SubmitSm newSubmitSm = new SubmitSm();

            // 复制原始属性
            newSubmitSm.setServiceType(original.getServiceType());
            newSubmitSm.setSourceAddress(original.getSourceAddress());
            newSubmitSm.setDestAddress(original.getDestAddress());
            newSubmitSm.setEsmClass(original.getEsmClass());
            newSubmitSm.setProtocolId(original.getProtocolId());
            newSubmitSm.setPriority(original.getPriority());
            newSubmitSm.setScheduleDeliveryTime(original.getScheduleDeliveryTime());
            newSubmitSm.setValidityPeriod(original.getValidityPeriod());
            newSubmitSm.setRegisteredDelivery(original.getRegisteredDelivery());
            newSubmitSm.setReplaceIfPresent(original.getReplaceIfPresent());
            newSubmitSm.setDataCoding(original.getDataCoding());
            newSubmitSm.setDefaultMsgId(original.getDefaultMsgId());

            // 设置UDH解析后的用户数据
            newSubmitSm.setShortMessage(userData);

            return newSubmitSm;
        } catch (Exception e) {
            log.error("创建UDH SubmitSm异常", e);
            return original; // 返回原始对象
        }
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cached_segments", segmentCache.size());
        stats.put("timeout_minutes", SEGMENT_TIMEOUT_MINUTES);
        
        // 按类型统计
        long sarCount = segmentCache.values().stream()
            .filter(group -> group.getType() == SegmentType.SAR).count();
        long udhCount = segmentCache.values().stream()
            .filter(group -> group.getType() == SegmentType.UDH).count();
            
        stats.put("sar_segments", sarCount);
        stats.put("udh_segments", udhCount);
        
        return stats;
    }
}
